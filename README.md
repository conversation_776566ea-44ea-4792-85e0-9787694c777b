# Password Reset Token Leak Vulnerability Lab

![Password Reset Vulnerability Lab](https://img.shields.io/badge/Security-Educational-blue)
![PHP](https://img.shields.io/badge/PHP-8.1-purple)
![MySQL](https://img.shields.io/badge/MySQL-8.0-orange)
![Docker](https://img.shields.io/badge/Docker-Ready-brightgreen)

A hands-on cybersecurity lab demonstrating a common vulnerability in password reset flows where the reset token is leaked in the API response. This lab is designed for educational purposes to help developers and security professionals understand how to identify and mitigate this security risk.

## 🔐 Vulnerability Overview

Password reset functionality is a common feature in web applications that allows users to regain access to their accounts when they forget their passwords. This process typically involves:

1. User requests a password reset by providing their email
2. Application generates a unique token and sends it to the user's email
3. User clicks the link with the token and sets a new password

In this lab, we demonstrate a critical vulnerability where the password reset token is leaked in the JSON response of the API call, even though the frontend only shows a generic message to the user. This allows an attacker to intercept the token by examining the network traffic and use it to reset any user's password without having access to their email.

## 🚀 Quick Start with Docker

### Prerequisites
- [Docker](https://www.docker.com/get-started) and Docker Compose installed

### Installation and Setup

1. Clone this repository:
   ```bash
   git clone https://github.com/yourusername/password-reset-vulnerability-lab.git
   cd password-reset-vulnerability-lab
   ```

2. Start the containers:
   ```bash
   docker-compose up -d
   ```

3. Access the lab at: http://localhost:8080

## 🔧 Manual Setup (Alternative)

### Prerequisites
- PHP 7.4+ with PDO MySQL extension
- MySQL/MariaDB server
- Web server (Apache/Nginx)

### Installation Steps

1. Clone or download this repository to your web server's document root
2. Create a MySQL database named `password_reset_lab`
3. Import the `database.sql` file into your database
4. Update database connection settings in `includes/db_connect.php` if needed
5. Access the lab through your web server

## 👤 Default Accounts

- **Regular User:**
  - Email: `<EMAIL>`
  - Password: `User@123`

- **Admin User (Target):**
  - Email: `<EMAIL>`
  - Password: `Admin@123`

## 🎯 Lab Objective

The objective of this lab is to exploit the password reset token leak vulnerability to gain admin access and capture the flag. This simulates a real-world attack scenario where an attacker can take over high-privilege accounts.

## 🔍 Step-by-Step Walkthrough

1. **Understand the Vulnerability:**
   - Start by reading the explanation on the home page
   - Learn how password reset flows should be implemented securely

2. **Exploit the Vulnerability:**
   - Go to the Forgot Password page
   - Request a password reset for the admin account (`<EMAIL>`)
   - Open your browser's Developer Tools (F12) and go to the Network tab
   - Find the API request to `api/request-reset.php`
   - Examine the response JSON and locate the leaked reset token

3. **Reset the Admin Password:**
   - Go to the Reset Password page
   - Enter the token you found in the API response
   - Set a new password for the admin account

4. **Capture the Flag:**
   - Login as admin with your new password
   - View the flag displayed on the admin dashboard

## 🛡️ Security Mitigations

To properly secure password reset functionality, implement these best practices:

1. **Secure Token Generation:**
   - Use cryptographically strong random tokens with sufficient entropy
   - Example: `bin2hex(random_bytes(32))` in PHP

2. **Secure Token Storage:**
   - Store tokens securely in your database with an expiration time
   - Hash tokens before storing them if possible

3. **Secure Token Delivery:**
   - Send tokens only through secure channels like email
   - Never return tokens in API responses
   - Use generic success messages regardless of whether the email exists

4. **Token Verification:**
   - Verify the token is valid and not expired before allowing password reset
   - Implement rate limiting on verification attempts

5. **One-Time Use:**
   - Invalidate tokens after they've been used

## 📁 Project Structure

```
├── api/                  # API endpoints
│   ├── request-reset.php # Vulnerable endpoint that leaks the token
│   └── reset-password.php # Endpoint to process password reset
├── assets/               # Frontend assets
│   ├── css/              # Stylesheets
│   └── js/               # JavaScript files
├── includes/             # Reusable PHP components
│   ├── config.php        # Application configuration
│   ├── db_connect.php    # Database connection
│   ├── header.php        # Page header
│   └── footer.php        # Page footer
├── dashboard.php         # User dashboard with flag for admin
├── database.sql          # Database schema and initial data
├── docker-compose.yml    # Docker Compose configuration
├── Dockerfile            # Docker configuration
├── forgot-password.php   # Password reset request form
├── index.php             # Main page with vulnerability explanation
├── login.php             # Login page
├── logout.php            # Logout functionality
├── README.md             # Project documentation
├── reset-password.php    # Password reset form
└── setup.php             # Database setup script
```

## 🔄 How the Vulnerability Works

The vulnerability exists in `api/request-reset.php` where the application includes the reset token in the JSON response:

```php
// VULNERABILITY: Token is leaked in the response
echo json_encode([
    'success' => true,
    'message' => 'Password reset link has been sent to your email',
    'debug_info' => [
        'user_id' => $user['id'],
        'email' => $email,
        'reset_token' => $token,  // This is the vulnerability!
        'expires' => date('Y-m-d H:i:s', strtotime('+1 hour'))
    ]
]);
```

Even though the frontend only displays a generic message to the user, the token is still present in the API response and can be viewed using browser developer tools.

## 🌐 Real-World Impact

This vulnerability has been found in many real-world applications and can lead to complete account takeover. For high-privilege accounts like administrators, this can result in a full system compromise.

Some notable examples include:

- Password reset token leaks in API responses
- Tokens sent in URL parameters and logged in server logs
- Tokens stored in browser history or referrer headers

## 📚 Learning Outcomes

By completing this lab, you will learn:

1. How password reset flows work in web applications
2. Common vulnerabilities in password reset implementations
3. How to identify token leakage in API responses
4. How to properly implement secure password reset functionality
5. Best practices for protecting sensitive tokens

## 🔄 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## ⚠️ Disclaimer

This lab is for educational purposes only. The vulnerabilities demonstrated should never be implemented in production systems. Always follow security best practices in real-world applications.
