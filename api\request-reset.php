<?php
require_once '../includes/config.php';

// Set content type to JSON
header('Content-Type: application/json');

// Get JSON input
$json = file_get_contents('php://input');
$data = json_decode($json, true);

// Check if email is provided
if (!isset($data['email']) || empty($data['email'])) {
    echo json_encode([
        'success' => false,
        'message' => 'Email is required'
    ]);
    exit;
}

$email = sanitizeInput($data['email']);

// Check if user exists
$stmt = $pdo->prepare("SELECT * FROM users WHERE email = ?");
$stmt->execute([$email]);
$user = $stmt->fetch();

if ($user) {
    // Generate reset token
    $token = generateResetToken();
    
    // Store token in database
    $stmt = $pdo->prepare("UPDATE users SET reset_token = ?, reset_token_expires = DATE_ADD(NOW(), INTERVAL 1 HOUR) WHERE id = ?");
    $stmt->execute([$token, $user['id']]);
    
    // In a real application, we would send an email with the reset link
    // For this lab, we're intentionally leaking the token in the response
    
    // VULNERABILITY: Token is leaked in the response
    echo json_encode([
        'success' => true,
        'message' => 'Password reset link has been sent to your email',
        'debug_info' => [
            'user_id' => $user['id'],
            'email' => $email,
            'reset_token' => $token,  // This is the vulnerability!
            'expires' => date('Y-m-d H:i:s', strtotime('+1 hour'))
        ]
    ]);
} else {
    // Don't reveal whether the email exists or not
    echo json_encode([
        'success' => true,
        'message' => 'If the email exists in our system, a password reset link has been sent.'
    ]);
}
?>
