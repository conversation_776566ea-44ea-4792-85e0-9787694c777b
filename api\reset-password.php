<?php
require_once '../includes/config.php';

// Set content type to JSON
header('Content-Type: application/json');

// Get JSON input
$json = file_get_contents('php://input');
$data = json_decode($json, true);

// Check if required fields are provided
if (!isset($data['token']) || empty($data['token']) || 
    !isset($data['password']) || empty($data['password'])) {
    echo json_encode([
        'success' => false,
        'message' => 'Token and password are required'
    ]);
    exit;
}

$token = sanitizeInput($data['token']);
$password = $data['password'];

// Validate password
if (strlen($password) < 8) {
    echo json_encode([
        'success' => false,
        'message' => 'Password must be at least 8 characters long'
    ]);
    exit;
}

// Verify token and update password
$stmt = $pdo->prepare("SELECT * FROM users WHERE reset_token = ? AND reset_token_expires > NOW()");
$stmt->execute([$token]);
$user = $stmt->fetch();

if ($user) {
    // Hash the new password
    $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
    
    // Update user's password and clear reset token
    $stmt = $pdo->prepare("UPDATE users SET password = ?, reset_token = NULL, reset_token_expires = NULL WHERE id = ?");
    $stmt->execute([$hashedPassword, $user['id']]);
    
    echo json_encode([
        'success' => true,
        'message' => 'Password has been reset successfully'
    ]);
} else {
    echo json_encode([
        'success' => false,
        'message' => 'Invalid or expired token'
    ]);
}
?>
