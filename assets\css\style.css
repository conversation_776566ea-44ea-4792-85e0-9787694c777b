/* Cyber Security Dark Theme for Cyber Bangla Lab */

@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;600;700;800;900&family=Rajdhani:wght@300;400;500;600;700&display=swap');

:root {
    /* Cyber Security Color Palette */
    --primary-gradient: linear-gradient(135deg, #0c1a2b, #1e3b5d);
    --secondary-gradient: linear-gradient(135deg, #00c9a7, #00a085);
    --danger-gradient: linear-gradient(135deg, #ff4757, #ff3742);
    --dark-gradient: linear-gradient(135deg, #0c1a2b, #1e3b5d);
    --light-gradient: linear-gradient(135deg, #1e3b5d, #2c5282);
    --cyber-gradient: linear-gradient(135deg, #00c9a7, #00ffcc, #00c9a7);

    /* Core Colors */
    --primary-color: #00c9a7;
    --secondary-color: #00ffcc;
    --dark-color: #0c1a2b;
    --light-color: #1e3b5d;
    --accent-color: #00a085;
    --text-primary: #ffffff;
    --text-secondary: #b8d4f0;
    --text-muted: #7a9cc6;

    /* Shadows with cyber glow effects */
    --card-shadow: 0 10px 30px rgba(0, 201, 167, 0.1), 0 6px 20px rgba(0, 0, 0, 0.3);
    --hover-shadow: 0 15px 40px rgba(0, 201, 167, 0.2), 0 10px 25px rgba(0, 0, 0, 0.4);
    --btn-shadow: 0 5px 20px rgba(0, 201, 167, 0.3);
    --glow-shadow: 0 0 20px rgba(0, 201, 167, 0.5);
}

/* Base Styles */
body {
    background: var(--primary-gradient);
    font-family: 'Rajdhani', 'Orbitron', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    min-height: 100vh;
    color: var(--text-primary);
    position: relative;
    overflow-x: hidden;
}

h1, h2, h3, h4, h5, h6 {
    color: var(--text-primary);
    font-family: 'Orbitron', sans-serif;
    font-weight: 600;
    text-shadow: 0 0 10px rgba(0, 201, 167, 0.3);
}

/* Cyber Grid Background */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        linear-gradient(rgba(0, 201, 167, 0.03) 1px, transparent 1px),
        linear-gradient(90deg, rgba(0, 201, 167, 0.03) 1px, transparent 1px);
    background-size: 50px 50px;
    z-index: -2;
    animation: gridMove 20s linear infinite;
}

/* Cyber particles effect */
body::after {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at 20% 80%, rgba(0, 201, 167, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(0, 255, 204, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(0, 201, 167, 0.05) 0%, transparent 50%);
    z-index: -1;
    animation: particleFloat 15s ease-in-out infinite;
}

/* Card Styles */
.card {
    border: 1px solid rgba(0, 201, 167, 0.2);
    border-radius: 15px;
    box-shadow: var(--card-shadow);
    transition: all 0.3s ease;
    overflow: hidden;
    backdrop-filter: blur(10px);
    background: linear-gradient(135deg, rgba(30, 59, 93, 0.9), rgba(12, 26, 43, 0.9));
    position: relative;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--cyber-gradient);
    animation: cyberPulse 2s ease-in-out infinite;
}

.card:hover {
    box-shadow: var(--hover-shadow);
    border-color: var(--primary-color);
    transform: translateY(-5px);
}

.card-header {
    background: var(--light-gradient);
    border-bottom: 1px solid rgba(0, 201, 167, 0.2);
    border-top-left-radius: 15px !important;
    border-top-right-radius: 15px !important;
    padding: 1.25rem 1.5rem;
    font-weight: 600;
    color: var(--text-primary);
    font-family: 'Orbitron', sans-serif;
}

/* Button Styles */
.btn {
    border-radius: 8px;
    padding: 0.75rem 2rem;
    font-weight: 600;
    letter-spacing: 1px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    font-size: 0.9rem;
    font-family: 'Orbitron', sans-serif;
    border: 1px solid transparent;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: var(--secondary-gradient);
    border: 1px solid var(--primary-color);
    color: var(--dark-color);
    box-shadow: var(--btn-shadow);
    text-shadow: none;
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: var(--glow-shadow);
    background: var(--cyber-gradient);
    color: var(--dark-color);
}

.btn-success {
    background: var(--secondary-gradient);
    border: 1px solid var(--primary-color);
    color: var(--dark-color);
    box-shadow: var(--btn-shadow);
}

.btn-success:hover {
    transform: translateY(-3px);
    box-shadow: var(--glow-shadow);
    background: var(--cyber-gradient);
    color: var(--dark-color);
}

/* Navbar Styles */
.navbar {
    background: var(--dark-gradient) !important;
    box-shadow: 0 2px 20px rgba(0, 201, 167, 0.2) !important;
    border-bottom: 1px solid rgba(0, 201, 167, 0.3) !important;
    backdrop-filter: blur(10px);
}

.navbar.navbar-dark {
    background: var(--dark-gradient) !important;
}

.navbar-brand {
    font-weight: 700;
    letter-spacing: 2px;
    color: var(--text-primary) !important;
    font-family: 'Orbitron', sans-serif;
    text-shadow: 0 0 10px rgba(0, 201, 167, 0.5);
    position: relative;
}

.navbar-brand i {
    color: var(--primary-color);
    text-shadow: 0 0 15px rgba(0, 201, 167, 0.8);
    animation: iconGlow 2s ease-in-out infinite alternate;
}

.nav-link {
    position: relative;
    margin: 0 10px;
    color: var(--text-secondary) !important;
    font-family: 'Rajdhani', sans-serif;
    font-weight: 500;
    letter-spacing: 1px;
    transition: all 0.3s ease;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--cyber-gradient);
    transition: width 0.3s ease;
    box-shadow: 0 0 5px var(--primary-color);
}

.nav-link:hover {
    color: var(--primary-color) !important;
    text-shadow: 0 0 8px rgba(0, 201, 167, 0.6);
}

.nav-link:hover::after {
    width: 100%;
}

/* Auth Card */
.auth-card {
    max-width: 500px;
    margin: 2rem auto;
    transform: translateY(0);
    transition: transform 0.5s ease;
    animation: fadeIn 1s ease;
}

.auth-card:hover {
    transform: translateY(-5px);
}

/* Flag Container */
.flag-container {
    background: var(--dark-gradient);
    color: var(--text-primary);
    border: 2px solid var(--primary-color);
    border-radius: 15px;
    padding: 2rem;
    font-family: 'Orbitron', monospace;
    font-size: 1.5rem;
    font-weight: bold;
    letter-spacing: 2px;
    margin: 2rem 0;
    text-align: center;
    box-shadow: var(--glow-shadow);
    position: relative;
    overflow: hidden;
    text-shadow: 0 0 10px rgba(0, 201, 167, 0.5);
}

.flag-container::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(0, 201, 167, 0.2), transparent);
    transform: rotate(45deg);
    animation: shine 3s infinite;
}

.flag-container::after {
    content: '🏆';
    position: absolute;
    top: 10px;
    right: 15px;
    font-size: 1.5rem;
    opacity: 0.8;
    filter: drop-shadow(0 0 5px var(--primary-color));
}

/* Feature Icons */
.feature-icon {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 1.5rem;
    animation: pulse 2s infinite;
    text-shadow: 0 0 15px rgba(0, 201, 167, 0.6);
    filter: drop-shadow(0 0 10px var(--primary-color));
}

/* API Response */
.api-response {
    background: var(--dark-gradient);
    color: var(--text-primary);
    padding: 1.5rem;
    border-radius: 10px;
    font-family: 'Fira Code', monospace;
    overflow-x: auto;
    box-shadow: var(--card-shadow);
    border: 1px solid rgba(0, 201, 167, 0.3);
    border-left: 4px solid var(--primary-color);
}

/* Card Hover Effects */
.vulnerability-card {
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    background: linear-gradient(135deg, rgba(30, 59, 93, 0.9), rgba(12, 26, 43, 0.9));
    border: 1px solid rgba(0, 201, 167, 0.2);
}

.vulnerability-card:hover {
    transform: translateY(-15px) scale(1.02);
    box-shadow: var(--hover-shadow);
    z-index: 1;
    border-color: var(--primary-color);
}

.vulnerability-card .card-body {
    padding: 2rem 1.5rem;
    position: relative;
    overflow: hidden;
    color: var(--text-primary);
}

.vulnerability-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: var(--cyber-gradient);
    transform: scaleX(0);
    transform-origin: left;
    transition: transform 0.4s ease;
}

.vulnerability-card:hover::before {
    transform: scaleX(1);
}

/* Code Block */
.code-block {
    background: var(--dark-gradient);
    color: var(--text-primary);
    border-radius: 10px;
    padding: 1.5rem;
    font-family: 'Fira Code', monospace;
    overflow-x: auto;
    box-shadow: var(--card-shadow);
    position: relative;
    border: 1px solid rgba(0, 201, 167, 0.3);
}

.code-block pre {
    margin: 0;
    color: var(--text-primary);
}

/* Form Controls */
.form-control {
    border-radius: 8px;
    padding: 0.75rem 1rem;
    border: 1px solid rgba(0, 201, 167, 0.3);
    background: rgba(30, 59, 93, 0.8);
    color: var(--text-primary);
    transition: all 0.3s ease;
    font-family: 'Rajdhani', sans-serif;
}

.form-control::placeholder {
    color: var(--text-muted);
}

.form-control:focus {
    box-shadow: 0 0 0 3px rgba(0, 201, 167, 0.25);
    border-color: var(--primary-color);
    background: rgba(30, 59, 93, 0.9);
    outline: none;
}

.input-group-text {
    background: var(--light-gradient);
    border: 1px solid rgba(0, 201, 167, 0.3);
    color: var(--text-primary);
    font-family: 'Rajdhani', sans-serif;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

@keyframes shine {
    0% { left: -100%; }
    100% { left: 100%; }
}

@keyframes gridMove {
    0% { transform: translate(0, 0); }
    100% { transform: translate(50px, 50px); }
}

@keyframes particleFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-20px) rotate(120deg); }
    66% { transform: translateY(10px) rotate(240deg); }
}

@keyframes cyberPulse {
    0%, 100% { opacity: 0.5; transform: scaleX(1); }
    50% { opacity: 1; transform: scaleX(1.05); }
}

@keyframes iconGlow {
    0% { text-shadow: 0 0 15px rgba(0, 201, 167, 0.8); }
    100% { text-shadow: 0 0 25px rgba(0, 201, 167, 1), 0 0 35px rgba(0, 201, 167, 0.8); }
}

/* Alert Styling */
.alert {
    border: 1px solid rgba(0, 201, 167, 0.3);
    border-radius: 10px;
    padding: 1rem 1.5rem;
    box-shadow: 0 3px 15px rgba(0, 201, 167, 0.1);
    position: relative;
    overflow: hidden;
    background: rgba(30, 59, 93, 0.8);
    color: var(--text-primary);
    backdrop-filter: blur(5px);
}

.alert-success {
    background: linear-gradient(135deg, rgba(0, 201, 167, 0.2), rgba(0, 160, 133, 0.2));
    border-left: 4px solid var(--primary-color);
    box-shadow: 0 0 15px rgba(0, 201, 167, 0.2);
}

.alert-danger {
    background: linear-gradient(135deg, rgba(255, 71, 87, 0.2), rgba(255, 55, 66, 0.2));
    border-left: 4px solid #ff4757;
    box-shadow: 0 0 15px rgba(255, 71, 87, 0.2);
}

.alert-info {
    background: linear-gradient(135deg, rgba(0, 201, 167, 0.2), rgba(0, 255, 204, 0.2));
    border-left: 4px solid var(--secondary-color);
    box-shadow: 0 0 15px rgba(0, 255, 204, 0.2);
}

/* Footer Styling */
footer {
    background: var(--dark-gradient) !important;
    padding: 2rem 0;
    position: relative;
    overflow: hidden;
    border-top: 1px solid rgba(0, 201, 167, 0.3);
    color: var(--text-primary) !important;
}

footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: var(--cyber-gradient);
    animation: cyberPulse 3s ease-in-out infinite;
}

footer p {
    color: var(--text-secondary) !important;
    font-family: 'Rajdhani', sans-serif;
    margin-bottom: 0.5rem !important;
}

footer .small {
    color: var(--text-muted) !important;
}

/* Bootstrap Overrides */
.card-body {
    color: var(--text-primary) !important;
}

.card-title {
    color: var(--text-primary) !important;
}

.card-text {
    color: var(--text-secondary) !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .auth-card {
        margin: 1rem auto;
    }

    .vulnerability-card:hover {
        transform: translateY(-5px) scale(1.01);
    }

    .btn {
        padding: 0.5rem 1.2rem;
    }
}
