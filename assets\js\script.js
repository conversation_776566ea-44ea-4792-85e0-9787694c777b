// Enhanced JavaScript for Password Reset Vulnerability Lab

document.addEventListener('DOMContentLoaded', function() {
    // Add animation classes to elements
    animateElements();

    // Handle forgot password form submission
    const forgotPasswordForm = document.getElementById('forgot-password-form');
    if (forgotPasswordForm) {
        const submitBtn = forgotPasswordForm.querySelector('button[type="submit"]');

        forgotPasswordForm.addEventListener('submit', function(e) {
            e.preventDefault();

            // Show loading state
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
            submitBtn.disabled = true;

            const email = document.getElementById('email').value;
            const responseContainer = document.getElementById('api-response');
            const responseMessage = document.getElementById('response-message');

            // Send AJAX request to the reset password API
            fetch('api/request-reset.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ email: email })
            })
            .then(response => response.json())
            .then(data => {
                // Reset button state
                submitBtn.innerHTML = 'Request Password Reset';
                submitBtn.disabled = false;

                // Store the API response in the hidden container (for lab purposes)
                const formattedJson = JSON.stringify(data, null, 2);
                responseContainer.innerHTML = formattedJson;
                // Note: We're not displaying the API response to the user

                // Show the generic success message
                responseMessage.style.display = 'block';
                responseMessage.style.opacity = '0';
                responseMessage.style.transform = 'translateY(20px)';

                setTimeout(() => {
                    responseMessage.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
                    responseMessage.style.opacity = '1';
                    responseMessage.style.transform = 'translateY(0)';
                }, 100);

                // If successful, show the lab instructions with animation
                if (data.success) {
                    const resetInstructions = document.getElementById('reset-instructions');
                    resetInstructions.style.display = 'block';
                    resetInstructions.style.opacity = '0';
                    resetInstructions.style.transform = 'translateY(20px)';

                    setTimeout(() => {
                        resetInstructions.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
                        resetInstructions.style.opacity = '1';
                        resetInstructions.style.transform = 'translateY(0)';
                    }, 500); // Delay this a bit more to show the success message first
                }
            })
            .catch(error => {
                console.error('Error:', error);
                submitBtn.innerHTML = 'Request Password Reset';
                submitBtn.disabled = false;
                // Store error in hidden container
                responseContainer.innerHTML = JSON.stringify({ error: 'An error occurred while processing your request.' }, null, 2);

                // Show generic message even on error
                responseMessage.style.display = 'block';
                responseMessage.style.opacity = '0';
                responseMessage.style.transform = 'translateY(20px)';

                setTimeout(() => {
                    responseMessage.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
                    responseMessage.style.opacity = '1';
                    responseMessage.style.transform = 'translateY(0)';
                }, 100);
            });
        });
    }

    // Copy flag to clipboard functionality with enhanced animation
    const copyFlagBtn = document.getElementById('copy-flag-btn');
    if (copyFlagBtn) {
        copyFlagBtn.addEventListener('click', function() {
            const flagText = document.getElementById('flag-text').textContent.trim();
            const flagContainer = document.querySelector('.flag-container');

            navigator.clipboard.writeText(flagText).then(function() {
                // Add success animation
                const originalText = copyFlagBtn.innerHTML;
                copyFlagBtn.innerHTML = '<i class="fas fa-check"></i> Copied!';
                copyFlagBtn.classList.add('btn-success');
                copyFlagBtn.classList.remove('btn-primary');

                // Create and add confetti effect
                const confettiContainer = document.createElement('div');
                confettiContainer.style.position = 'fixed';
                confettiContainer.style.top = '0';
                confettiContainer.style.left = '0';
                confettiContainer.style.width = '100%';
                confettiContainer.style.height = '100%';
                confettiContainer.style.pointerEvents = 'none';
                confettiContainer.style.zIndex = '9999';
                document.body.appendChild(confettiContainer);

                // Create confetti pieces
                for (let i = 0; i < 100; i++) {
                    const confetti = document.createElement('div');
                    confetti.style.position = 'absolute';
                    confetti.style.width = Math.random() * 10 + 5 + 'px';
                    confetti.style.height = Math.random() * 10 + 5 + 'px';
                    confetti.style.backgroundColor = ['#6e8efb', '#a777e3', '#42e695', '#3bb2b8', '#ff6b6b'][Math.floor(Math.random() * 5)];
                    confetti.style.borderRadius = '50%';
                    confetti.style.top = '-10px';
                    confetti.style.left = Math.random() * 100 + 'vw';
                    confetti.style.opacity = Math.random() + 0.5;
                    confetti.style.transform = 'scale(' + (Math.random() + 0.5) + ')';
                    confetti.style.animation = 'fall ' + (Math.random() * 3 + 2) + 's linear';
                    confettiContainer.appendChild(confetti);

                    // Remove confetti after animation
                    setTimeout(() => {
                        confetti.remove();
                    }, 5000);
                }

                // Add fall animation
                const style = document.createElement('style');
                style.textContent = `
                    @keyframes fall {
                        0% { transform: translateY(0) rotate(0deg); }
                        100% { transform: translateY(100vh) rotate(360deg); }
                    }
                `;
                document.head.appendChild(style);

                // Add pulse effect to flag container
                flagContainer.style.boxShadow = '0 0 30px rgba(255, 215, 0, 0.7)';
                flagContainer.style.transition = 'box-shadow 0.3s ease';
                flagContainer.style.transform = 'scale(1.05)';
                flagContainer.style.transition = 'transform 0.3s ease, box-shadow 0.3s ease';

                setTimeout(function() {
                    copyFlagBtn.innerHTML = originalText;
                    copyFlagBtn.classList.remove('btn-success');
                    copyFlagBtn.classList.add('btn-primary');
                    flagContainer.style.boxShadow = '';
                    flagContainer.style.transform = 'scale(1)';

                    // Remove confetti container
                    setTimeout(() => {
                        confettiContainer.remove();
                    }, 3000);
                }, 2000);
            });
        });
    }

    // Tooltips initialization
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Add scroll reveal animations
    window.addEventListener('scroll', revealOnScroll);
    revealOnScroll(); // Run once on page load
});

// Function to add animation classes to elements
function animateElements() {
    // Add staggered animation to cards
    const cards = document.querySelectorAll('.vulnerability-card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        card.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
        card.style.transitionDelay = `${index * 0.1}s`;

        setTimeout(() => {
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, 100);
    });

    // Add animation to feature icons
    const icons = document.querySelectorAll('.feature-icon');
    icons.forEach((icon, index) => {
        icon.style.opacity = '0';
        icon.style.transform = 'scale(0.5)';
        icon.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
        icon.style.transitionDelay = `${index * 0.1 + 0.3}s`;

        setTimeout(() => {
            icon.style.opacity = '1';
            icon.style.transform = 'scale(1)';
        }, 100);
    });
}

// Function to reveal elements on scroll
function revealOnScroll() {
    const elements = document.querySelectorAll('.row.mb-5');

    elements.forEach(element => {
        const elementTop = element.getBoundingClientRect().top;
        const elementVisible = 150;

        if (elementTop < window.innerHeight - elementVisible) {
            if (!element.classList.contains('animated')) {
                element.classList.add('animated');
                element.style.opacity = '0';
                element.style.transform = 'translateY(20px)';
                element.style.transition = 'opacity 0.8s ease, transform 0.8s ease';

                setTimeout(() => {
                    element.style.opacity = '1';
                    element.style.transform = 'translateY(0)';
                }, 100);
            }
        }
    });
}
