<?php
require_once 'includes/config.php';

// Check if user is logged in
if (!isLoggedIn()) {
    setFlashMessage('danger', 'Please login to access the dashboard.');
    redirect('login.php');
}

include 'includes/header.php';
?>

<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <?php if (isAdmin()): ?>
                <!-- Admin Dashboard - Simplified -->
                <div class="text-center mb-4">
                    <div class="display-1 mb-3"><i class="fas fa-crown" style="color: gold;"></i></div>
                    <h1 class="display-4 fw-bold">Admin Access Granted</h1>
                    <p class="lead">Welcome, <strong><?php echo htmlspecialchars($_SESSION['email']); ?></strong></p>
                </div>

                <div class="card shadow-lg border-0 mb-5">
                    <div class="card-header bg-primary text-white py-3">
                        <h2 class="mb-0 text-center"><i class="fas fa-flag me-2"></i>Capture The Flag</h2>
                    </div>
                    <div class="card-body p-5">
                        <div class="alert alert-success">
                            <p class="mb-0">You've successfully exploited the password reset vulnerability!</p>
                        </div>

                        <div class="flag-container my-4" id="flag-text">
                            <?php echo FLAG; ?>
                        </div>

                        <div class="text-center">
                            <button id="copy-flag-btn" class="btn btn-lg btn-primary">
                                <i class="fas fa-copy me-2"></i>Copy Flag
                            </button>
                            <a href="logout.php" class="btn btn-lg btn-outline-secondary ms-2">
                                <i class="fas fa-sign-out-alt me-2"></i>Logout
                            </a>
                        </div>
                    </div>
                </div>
            <?php else: ?>
                <!-- Regular User Dashboard - Simplified -->
                <div class="text-center mb-4">
                    <div class="display-1 mb-3"><i class="fas fa-user" style="color: var(--primary-color);"></i></div>
                    <h1 class="display-4 fw-bold">Welcome, <?php echo htmlspecialchars($_SESSION['email']); ?></h1>
                </div>

                <div class="card shadow-lg border-0">
                    <div class="card-body p-5 text-center">
                        <div class="alert alert-info mb-4">
                            <h4 class="alert-heading"><i class="fas fa-info-circle me-2"></i>Challenge</h4>
                            <p class="mb-0">You are logged in as a regular user. Try to gain admin access to capture the flag!</p>
                        </div>

                        <div class="d-grid gap-3">
                            <a href="forgot-password.php" class="btn btn-lg btn-primary">
                                <i class="fas fa-key me-2"></i>Try Password Reset
                            </a>
                            <a href="logout.php" class="btn btn-lg btn-outline-secondary">
                                <i class="fas fa-sign-out-alt me-2"></i>Logout
                            </a>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
