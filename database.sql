-- Database setup for Password Reset Flow Vulnerability Lab

-- Create database if not exists
CREATE DATABASE IF NOT EXISTS password_reset_lab;
USE password_reset_lab;

-- Create users table
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(100) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    is_admin BOOLEAN DEFAULT FALSE,
    reset_token VARCHAR(100) NULL,
    reset_token_expires DATETIME NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert admin user with flag
-- Default password: Admin@123
INSERT INTO users (username, email, password, is_admin) VALUES
('admin', '<EMAIL>', '$2y$10$3HyRfe2xwWzpmMBOdUqn8eQq1b6B89yaUbaVew4/VTu.G78d1Xuxa', 1);

-- Insert regular user
-- Default password: User@123
INSERT INTO users (username, email, password, is_admin) VALUES
('user', '<EMAIL>', '$2y$10$qub5MoK0kga2uhgP6tLb3.1Sd9FfmhZtBEi46wnTKk8Xf/VqNlj2.', 0);
