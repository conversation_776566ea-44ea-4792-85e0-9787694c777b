version: '3.8'

services:
  web:
    container_name: password-reset-token-leak-lab
    build: .
    ports:
      - "8080:80"
    environment:
      - DB_HOST=db
      - DB_NAME=password_reset_lab
      - DB_USER=root
      - DB_PASSWORD=rootpassword
    depends_on:
      - db
    volumes:
      - .:/var/www/html

  db:
    container_name: password-reset-token-leak-lab-db
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=rootpassword
      - MYSQL_DATABASE=password_reset_lab
    ports:
      - "3306:3306"
    volumes:
      - db_data:/var/lib/mysql
      - ./database.sql:/docker-entrypoint-initdb.d/database.sql

volumes:
  db_data:
