<?php
require_once 'includes/config.php';

// Check if user is already logged in
if (isLoggedIn()) {
    redirect('dashboard.php');
}

include 'includes/header.php';
?>

<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card auth-card">
            <div class="card-header">
                <h3 class="text-center">Forgot Password</h3>
            </div>
            <div class="card-body">
                <p class="text-center mb-4">Enter your email address to request a password reset.</p>

                <form id="forgot-password-form">
                    <div class="mb-3">
                        <label for="email" class="form-label">Email Address</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                            <input type="email" class="form-control" id="email" name="email" required>
                        </div>
                        <div class="form-text"><NAME_EMAIL> for this lab.</div>
                    </div>
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">Request Password Reset</button>
                    </div>
                </form>

                <div id="response-message" class="mt-4 alert alert-success" style="display: none;">
                    <h5><i class="fas fa-check-circle me-2"></i>Request Submitted</h5>
                    <p>If the email exists in our system, a password reset link will be sent to that address.</p>
                </div>

                <!-- Hidden API response for lab purposes (not visible to user) -->
                <pre id="api-response" class="api-response" style="display: none;"></pre>

                <div id="reset-instructions" class="mt-4 alert alert-info" style="display: none;">
                    <h5><i class="fas fa-info-circle me-2"></i>Lab Instructions</h5>
                    <p>For this lab, we need to examine what's happening behind the scenes.</p>
                    <p>Open your browser's Developer Tools (F12) and go to the Network tab.</p>
                    <p>Look at the response from the API request - you'll notice the reset token is being leaked!</p>
                    <p>Copy the token and go to the <a href="reset-password.php">Reset Password</a> page to complete the exploit.</p>
                </div>
            </div>
            <div class="card-footer text-center">
                <a href="login.php">Back to Login</a>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
