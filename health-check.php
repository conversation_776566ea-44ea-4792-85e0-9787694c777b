<?php
// Simple health check for Docker
header('Content-Type: application/json');

$status = [
    'status' => 'ok',
    'timestamp' => date('Y-m-d H:i:s'),
    'service' => 'password-reset-lab'
];

// Check database connection
try {
    $host = getenv('DB_HOST') ?: 'localhost';
    $dbname = getenv('DB_NAME') ?: 'password_reset_lab';
    $username = getenv('DB_USER') ?: 'root';
    $password = getenv('DB_PASSWORD') ?: '';
    
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $status['database'] = 'connected';
} catch (PDOException $e) {
    $status['status'] = 'error';
    $status['database'] = 'disconnected';
    $status['error'] = 'Database connection failed';
}

echo json_encode($status, JSON_PRETTY_PRINT);
?>
