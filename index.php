<?php include 'includes/header.php'; ?>

<div class="row mb-4">
    <div class="col-md-12 text-center">
        <h1 class="display-4 mb-3">Password Reset Vulnerability Lab</h1>
        <p class="lead">Learn about insecure password reset flows and how to protect against them</p>
    </div>
</div>

<div class="row mb-5">
    <div class="col-md-4 mb-4">
        <div class="card h-100 vulnerability-card">
            <div class="card-body text-center">
                <div class="feature-icon">
                    <i class="fas fa-key"></i>
                </div>
                <h5 class="card-title">Vulnerable Reset Flow</h5>
                <p class="card-text">This lab demonstrates a common vulnerability where password reset tokens are leaked in API responses.</p>
                <a href="forgot-password.php" class="btn btn-primary">Try It Out</a>
            </div>
        </div>
    </div>
    <div class="col-md-4 mb-4">
        <div class="card h-100 vulnerability-card">
            <div class="card-body text-center">
                <div class="feature-icon">
                    <i class="fas fa-user-shield"></i>
                </div>
                <h5 class="card-title">Admin Access</h5>
                <p class="card-text">Exploit the vulnerability to gain admin access and capture the flag.</p>
                <a href="login.php" class="btn btn-primary">Login</a>
            </div>
        </div>
    </div>
    <div class="col-md-4 mb-4">
        <div class="card h-100 vulnerability-card">
            <div class="card-body text-center">
                <div class="feature-icon">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <h5 class="card-title">Security Mitigations</h5>
                <p class="card-text">Learn how to properly implement secure password reset flows.</p>
                <a href="#mitigations" class="btn btn-primary">Learn More</a>
            </div>
        </div>
    </div>
</div>

<div class="row mb-5">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h2>About This Vulnerability</h2>
            </div>
            <div class="card-body">
                <h4>What is a Password Reset Token Leak?</h4>
                <p>
                    Password reset functionality is a common feature in web applications that allows users to regain access to their accounts when they forget their passwords.
                    This process typically involves generating a unique token that is sent to the user's email and is required to set a new password.
                </p>
                <p>
                    In this lab, we demonstrate a vulnerability where the password reset token is leaked in the JSON response of the API call,
                    even though the frontend only shows a generic message to the user.
                    This allows an attacker to intercept the token by examining the network traffic and use it to reset any user's password without having access to their email.
                </p>

                <h4>The Attack Scenario</h4>
                <p>The attack works as follows:</p>
                <ol>
                    <li>An attacker initiates a password reset for a target user (e.g., <EMAIL>)</li>
                    <li>The application generates a reset token but mistakenly includes it in the API response</li>
                    <li>The attacker captures this token from the response</li>
                    <li>The attacker uses the token to reset the user's password</li>
                    <li>The attacker can now log in as the target user</li>
                </ol>

                <h4>Real-World Impact</h4>
                <p>
                    This vulnerability has been found in many real-world applications and can lead to complete account takeover.
                    For high-privilege accounts like administrators, this can result in a full system compromise.
                </p>
            </div>
        </div>
    </div>
</div>

<div class="row mb-5" id="mitigations">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h2>Security Mitigations</h2>
            </div>
            <div class="card-body">
                <h4>How to Properly Implement Password Reset</h4>
                <p>To implement a secure password reset flow, follow these best practices:</p>

                <h5>1. Secure Token Generation</h5>
                <p>
                    Generate cryptographically strong random tokens with sufficient entropy.
                </p>
                <div class="code-block">
                    <pre>// PHP example
$token = bin2hex(random_bytes(32)); // 64 characters hex string</pre>
                </div>

                <h5>2. Secure Token Storage</h5>
                <p>
                    Store tokens securely in your database with an expiration time.
                </p>
                <div class="code-block">
                    <pre>// SQL example
UPDATE users SET
    reset_token = ?,
    reset_token_expires = DATE_ADD(NOW(), INTERVAL 1 HOUR)
WHERE email = ?</pre>
                </div>

                <h5>3. Secure Token Delivery</h5>
                <p>
                    Send tokens only through secure channels like email. Never return tokens in API responses.
                </p>
                <div class="code-block">
                    <pre>// Correct API response
{
    "success": true,
    "message": "If the email exists in our system, a password reset link has been sent."
}</pre>
                </div>

                <h5>4. Token Verification</h5>
                <p>
                    Verify the token is valid and not expired before allowing password reset.
                </p>
                <div class="code-block">
                    <pre>// PHP example
$stmt = $pdo->prepare("SELECT * FROM users WHERE reset_token = ? AND reset_token_expires > NOW()");
$stmt->execute([$token]);
$user = $stmt->fetch();

if (!$user) {
    // Invalid or expired token
    die("Invalid or expired token");
}</pre>
                </div>

                <h5>5. One-Time Use</h5>
                <p>
                    Invalidate tokens after they've been used.
                </p>
                <div class="code-block">
                    <pre>// PHP example
$stmt = $pdo->prepare("UPDATE users SET reset_token = NULL, reset_token_expires = NULL WHERE id = ?");
$stmt->execute([$user_id]);</pre>
                </div>

                <h5>6. Rate Limiting</h5>
                <p>
                    Implement rate limiting to prevent brute force attacks on the reset endpoint.
                </p>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h2>Lab Instructions</h2>
            </div>
            <div class="card-body">
                <p>Follow these steps to complete the lab:</p>
                <ol>
                    <li>Go to the <a href="forgot-password.php">Forgot Password</a> page</li>
                    <li>Request a password <NAME_EMAIL></li>
                    <li>Open your browser's Developer Tools (F12) and go to the Network tab</li>
                    <li>Observe the API response in the Network tab to find the reset token</li>
                    <li>Use the token to reset the admin's password</li>
                    <li>Login as admin with your new password</li>
                    <li>Capture the flag from the admin dashboard</li>
                </ol>
                <p class="mt-4">
                    <a href="login.php" class="btn btn-primary">Start the Lab</a>
                </p>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
