<?php
require_once 'includes/config.php';

// Check if user is already logged in
if (isLoggedIn()) {
    redirect('dashboard.php');
}

// Process login form
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $email = sanitizeInput($_POST['email']);
    $password = $_POST['password'];

    // Validate input
    if (empty($email) || empty($password)) {
        setFlashMessage('danger', 'Please enter both email and password.');
    } else {
        // Check user credentials
        $stmt = $pdo->prepare("SELECT * FROM users WHERE email = ?");
        $stmt->execute([$email]);
        $user = $stmt->fetch();

        if ($user && password_verify($password, $user['password'])) {
            // Set session variables
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['email'] = $user['email'];
            $_SESSION['username'] = $user['username']; // Keep this for backward compatibility
            $_SESSION['is_admin'] = $user['is_admin'];

            setFlashMessage('success', 'Login successful!');
            redirect('dashboard.php');
        } else {
            setFlashMessage('danger', 'Invalid username or password.');
        }
    }
}

include 'includes/header.php';
?>

<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card auth-card">
            <div class="card-header">
                <h3 class="text-center">Login</h3>
            </div>
            <div class="card-body">
                <form method="POST" action="login.php">
                    <div class="mb-3">
                        <label for="email" class="form-label">Email Address</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                            <input type="email" class="form-control" id="email" name="email" required>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="password" class="form-label">Password</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-lock"></i></span>
                            <input type="password" class="form-control" id="password" name="password" required>
                        </div>
                    </div>
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">Login</button>
                    </div>
                </form>
                <div class="text-center mt-3">
                    <a href="forgot-password.php">Forgot Password?</a>
                </div>
            </div>
            <div class="card-footer text-center">
                <p class="mb-0">Default accounts for testing:</p>
                <p class="mb-0 small">Email: <strong><EMAIL></strong> / Password: <strong>User@123</strong></p>
                <p class="mb-0 small">Email: <strong><EMAIL></strong> / Password: <strong>Admin@123</strong></p>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
