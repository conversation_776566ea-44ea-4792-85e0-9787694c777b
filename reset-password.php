<?php
require_once 'includes/config.php';

// Check if user is already logged in
if (isLoggedIn()) {
    redirect('dashboard.php');
}

$token = isset($_GET['token']) ? sanitizeInput($_GET['token']) : '';
$email = isset($_GET['email']) ? sanitizeInput($_GET['email']) : '';
$validToken = false;
$tokenError = '';

// Validate token if provided
if (!empty($token)) {
    $stmt = $pdo->prepare("SELECT * FROM users WHERE reset_token = ? AND reset_token_expires > NOW()");
    $stmt->execute([$token]);
    $user = $stmt->fetch();

    if ($user) {
        $validToken = true;
        $email = $user['email'];
    } else {
        $tokenError = 'Invalid or expired token. Please request a new password reset.';
    }
}

// Process password reset form
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['token']) && isset($_POST['password'])) {
    $token = sanitizeInput($_POST['token']);
    $password = $_POST['password'];
    $confirmPassword = $_POST['confirm_password'];

    // Validate input
    if (empty($password) || empty($confirmPassword)) {
        setFlashMessage('danger', 'Please enter both password fields.');
    } elseif ($password !== $confirmPassword) {
        setFlashMessage('danger', 'Passwords do not match.');
    } elseif (strlen($password) < 8) {
        setFlashMessage('danger', 'Password must be at least 8 characters long.');
    } else {
        // Verify token and update password
        $stmt = $pdo->prepare("SELECT * FROM users WHERE reset_token = ? AND reset_token_expires > NOW()");
        $stmt->execute([$token]);
        $user = $stmt->fetch();

        if ($user) {
            // Hash the new password
            $hashedPassword = password_hash($password, PASSWORD_DEFAULT);

            // Update user's password and clear reset token
            $stmt = $pdo->prepare("UPDATE users SET password = ?, reset_token = NULL, reset_token_expires = NULL WHERE id = ?");
            $stmt->execute([$hashedPassword, $user['id']]);

            setFlashMessage('success', 'Your password has been reset successfully. You can now login with your new password.');
            redirect('login.php');
        } else {
            setFlashMessage('danger', 'Invalid or expired token. Please request a new password reset.');
        }
    }
}

include 'includes/header.php';
?>

<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card auth-card">
            <div class="card-header">
                <h3 class="text-center">Reset Password</h3>
            </div>
            <div class="card-body">
                <?php if ($tokenError): ?>
                    <div class="alert alert-danger">
                        <?php echo $tokenError; ?>
                    </div>
                    <div class="text-center">
                        <a href="forgot-password.php" class="btn btn-primary">Request New Reset Link</a>
                    </div>
                <?php elseif (!$token): ?>
                    <form method="GET" action="reset-password.php">
                        <div class="alert alert-info">
                            <p><i class="fas fa-info-circle me-2"></i>Enter the reset token you found in the API response.</p>
                        </div>
                        <div class="mb-3">
                            <label for="token" class="form-label">Reset Token</label>
                            <input type="text" class="form-control" id="token" name="token" required>
                        </div>
                        <div class="mb-3">
                            <label for="email" class="form-label">Email Address</label>
                            <input type="email" class="form-control" id="email" name="email" value="<EMAIL>" required>
                        </div>
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">Verify Token</button>
                        </div>
                    </form>
                <?php else: ?>
                    <p class="text-center mb-4">Create a new password for <strong><?php echo htmlspecialchars($email); ?></strong></p>

                    <form method="POST" action="reset-password.php">
                        <input type="hidden" name="token" value="<?php echo htmlspecialchars($token); ?>">

                        <div class="mb-3">
                            <label for="password" class="form-label">New Password</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                <input type="password" class="form-control" id="password" name="password" required>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="confirm_password" class="form-label">Confirm New Password</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                            </div>
                        </div>
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">Reset Password</button>
                        </div>
                    </form>
                <?php endif; ?>
            </div>
            <div class="card-footer text-center">
                <a href="login.php">Back to Login</a>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
