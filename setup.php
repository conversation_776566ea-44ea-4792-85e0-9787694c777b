<?php
// Database setup script for Password Reset Vulnerability Lab

// Database configuration
$host = getenv('DB_HOST') ?: 'localhost';
$username = getenv('DB_USER') ?: 'root';
$password = getenv('DB_PASSWORD') ?: '';

try {
    // Connect to MySQL server
    $pdo = new PDO("mysql:host=$host", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Read SQL file
    $sql = file_get_contents('database.sql');

    // Execute SQL commands
    $pdo->exec($sql);

    echo "<div style='font-family: Arial, sans-serif; max-width: 600px; margin: 50px auto; padding: 20px; border-radius: 10px; box-shadow: 0 0 20px rgba(0,0,0,0.1);'>";
    echo "<h1 style='color: #4e73df;'>Database Setup Complete</h1>";
    echo "<p style='font-size: 16px;'>The database has been successfully initialized.</p>";
    echo "<p style='font-size: 16px;'>You can now access the lab at <a href='index.php' style='color: #4e73df; text-decoration: none;'>index.php</a></p>";
    echo "<div style='margin-top: 20px; padding: 15px; background-color: #f8f9fc; border-left: 4px solid #4e73df; border-radius: 5px;'>";
    echo "<strong>Default Accounts:</strong><br>";
    echo "Admin: <EMAIL> / Admin@123<br>";
    echo "User: <EMAIL> / User@123";
    echo "</div>";
    echo "</div>";

} catch (PDOException $e) {
    die("Database setup failed: " . $e->getMessage());
}
?>
